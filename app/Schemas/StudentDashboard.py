from pydantic import BaseModel, Field
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime, timezone
from Schemas.users import UserOut
from Schemas.TeacherModule.Classroom import ClassroomOutForStudent
from Schemas.TeacherModule.tasks import StudentTaskOut
from Schemas.Exams.Exam import ExamStudentMinimalOut

class QuickAction(BaseModel):
    id: str = Field(..., description="Unique identifier for the action")
    title: str = Field(..., description="Action title")
    description: str = Field(..., description="Action description")
    action_type: str = Field(..., description="Type of action (task, exam, announcement, etc.)")
    url: Optional[str] = Field(None, description="URL to navigate to")
    priority: str = Field(default="medium", description="Priority level (high, medium, low)")
    due_date: Optional[datetime] = Field(None, description="Due date if applicable")

class StudyMetrics(BaseModel):
    total_points: int = Field(default=0, description="Total gamification points earned")
    level: int = Field(default=1, description="Current student level")
    streak_days: int = Field(default=0, description="Current study streak in days")
    tasks_completed: int = Field(default=0, description="Total tasks completed")
    exams_taken: int = Field(default=0, description="Total exams taken")
    average_grade: float = Field(default=0.0, description="Average grade across all assessments")
    badges_earned: List[str] = Field(default_factory=list, description="List of badges earned")

class PerformanceMetrics(BaseModel):
    overall_grade: float = Field(default=0.0, description="Overall academic performance")
    subject_grades: Dict[str, float] = Field(default_factory=dict, description="Grades by subject")
    recent_scores: List[Dict[str, Any]] = Field(default_factory=list, description="Recent exam/task scores")
    improvement_trend: str = Field(default="stable", description="Performance trend (improving, declining, stable)")
    rank_in_class: Optional[int] = Field(None, description="Current rank in class")

class AttendanceData(BaseModel):
    total_classes: int = Field(default=0, description="Total classes scheduled")
    attended_classes: int = Field(default=0, description="Classes attended")
    attendance_percentage: float = Field(default=0.0, description="Attendance percentage")
    recent_attendance: List[Dict[str, Any]] = Field(default_factory=list, description="Recent attendance records")

class RecentActivity(BaseModel):
    id: str = Field(..., description="Activity ID")
    type: str = Field(..., description="Activity type (task_submitted, exam_completed, etc.)")
    title: str = Field(..., description="Activity title")
    description: str = Field(..., description="Activity description")
    timestamp: datetime = Field(..., description="When the activity occurred")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional activity data")

class ScheduleItem(BaseModel):
    id: str = Field(..., description="Schedule item ID")
    title: str = Field(..., description="Event title")
    type: str = Field(..., description="Event type (class, exam, assignment_due, etc.)")
    start_time: datetime = Field(..., description="Event start time")
    end_time: Optional[datetime] = Field(None, description="Event end time")
    classroom_id: Optional[UUID] = Field(None, description="Associated classroom ID")
    subject: Optional[str] = Field(None, description="Subject name")
    location: Optional[str] = Field(None, description="Event location")

class StudentDashboardData(BaseModel):
    success: bool = Field(default=True, description="API response success status")
    data: 'DashboardData' = Field(..., description="Dashboard data")

class DashboardData(BaseModel):
    student: UserOut = Field(..., description="Student user information")
    classes: List[ClassroomOutForStudent] = Field(default_factory=list, description="Student's enrolled classes")
    exams: List[ExamStudentMinimalOut] = Field(default_factory=list, description="Upcoming and recent exams")
    performance: PerformanceMetrics = Field(default_factory=PerformanceMetrics, description="Academic performance metrics")
    assignments: List[StudentTaskOut] = Field(default_factory=list, description="Current assignments/tasks")
    attendance: AttendanceData = Field(default_factory=AttendanceData, description="Attendance information")
    recent_activity: List[RecentActivity] = Field(default_factory=list, description="Recent student activities")
    study_metrics: StudyMetrics = Field(default_factory=StudyMetrics, description="Gamification and study metrics")
    schedule: List[ScheduleItem] = Field(default_factory=list, description="Upcoming schedule items")
    quick_actions: List[QuickAction] = Field(default_factory=list, description="Actionable items for the student")
    unread_notifications_count: int = Field(default=0, description="Number of unread notifications")
    last_updated: datetime = Field(default_factory=lambda: datetime.now(timezone.utc), description="When the dashboard was last updated")

    class Config:
        from_attributes = True
        use_enum_values = True
