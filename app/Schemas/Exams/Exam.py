from pydantic import BaseModel, Field
from typing import Optional, List
from uuid import UUID
from datetime import datetime, timedelta
from Schemas.Exams.Questions import QuestionOut, QuestionCreate, QuestionStudentOut

class ExamBase(BaseModel):
    title: str
    description: Optional[str] = None
    total_marks: int = 0
    total_duration: int = 0  # in minutes
    start_time: Optional[datetime] = None

class ExamCreate(ExamBase):
    question_ids: List[UUID] = Field(default_factory=list)

class ExamUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    total_marks: Optional[int] = None
    total_duration: Optional[int] = None
    question_ids: Optional[List[UUID]] = None
    start_time: Optional[datetime] = None

class ExamOut(ExamBase):
    id: UUID
    questions: List[QuestionOut] = []
    end_time: Optional[datetime] = None
    class Config:
        orm_mode = True

    @staticmethod
    def from_orm_with_end_time(obj):
        data = ExamOut.from_orm(obj)
        if obj.start_time and obj.total_duration:
            data.end_time = obj.start_time + timedelta(minutes=obj.total_duration)
        else:
            data.end_time = None
        return data

# New schemas for assignment
class ExamAssignmentIn(BaseModel):
    student_ids: Optional[List[UUID]] = None
    classroom_id: Optional[UUID] = None

class ExamCreateWithAssignment(ExamBase):
    questions: List[QuestionCreate] = Field(default_factory=list)
    assignment: ExamAssignmentIn

class ExamAssignmentOut(BaseModel):
    exam_id: UUID
    assigned_student_ids: List[UUID]

class ExamStudentOut(BaseModel):
    id: UUID
    questions: List[QuestionStudentOut] = []
    end_time: Optional[datetime] = None
    class Config:
        orm_mode = True

class ExamStudentMinimalOut(BaseModel):
    id: UUID
    title: str
    description: Optional[str] = None
    total_marks: int = 0
    total_duration: int = 0
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    class Config:
        orm_mode = True