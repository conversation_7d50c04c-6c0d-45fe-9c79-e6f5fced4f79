from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from Models.Tasks import *
from Models.ExamSession import *  # Import the new exam session models
from config.session import Base, engine
from Routes.users import router as user_router
from Routes.TeacherModule.Classroom import router as classroom_router
from Routes.Exams.Subjects import router as subject_router
from Routes.Exams.Chapters import router as chapter_router
from Routes.Exams.Topics import router as topic_router
from Routes.Exams.Subtopics import router as subtopic_router
from Routes.TeacherModule.Tasks import router as task_router
from Routes.TeacherModule.TeacherProfile import router as teacher_profile_router
from Routes.TeacherSubscription import router as teacher_subscription_router
from Routes.Exams.Plan import router as plan_router
from Routes.TeacherModule.announcement import router as announcement_router
from Routes.TeacherModule.Class import router as class_router
from Routes.Exams.Questions import router as question_router
from Routes.Exams.Exam import router as exam_router
from Routes.Exams.ExamSession import router as exam_session_router
from Routes.Exams.ExamChecking import router as exam_checking_router
from Routes.StudentDashboard import router as student_dashboard_router
from config.redis import init_redis, close_redis, get_redis
import asyncio
from sqlalchemy.orm import Session
from config.session import get_db
from datetime import datetime, timedelta
from Models.Exam import StudentExamAttempt, StudentExamAnswer
import json
from config.mongodb import init_mongodb

app = FastAPI()

# Create the database tables
Base.metadata.create_all(bind=engine)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Add frontend origins here
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)

# Include routers
app.include_router(user_router, prefix="/api/users", tags=["users"])
app.include_router(classroom_router, prefix="/api/classrooms", tags=["classrooms"])
app.include_router(subject_router, prefix="/api/subjects", tags=["subjects"])
app.include_router(chapter_router, prefix="/api/chapters", tags=["chapters"])
app.include_router(topic_router, prefix="/api/topics", tags=["topics"])
app.include_router(subtopic_router, prefix="/api/subtopics", tags=["subtopics"])
app.include_router(task_router, prefix="/api/tasks", tags=["tasks"])
app.include_router(teacher_profile_router, prefix="/api/teachers", tags=["teacherProfiles"])
app.include_router(teacher_subscription_router, prefix="/api/teacher-subscriptions", tags=["teacherSubscriptions"])    
app.include_router(plan_router, prefix="/api/plans", tags=["plans"])
app.include_router(announcement_router, prefix="/api/announcements", tags=["announcements"])
app.include_router(class_router, prefix="/api/classes", tags=["classes"])
app.include_router(question_router, prefix="/api/questions", tags=["questions"])
app.include_router(exam_router, prefix="/api/exams", tags=["exams"])
app.include_router(exam_session_router, prefix="/api/exams/session", tags=["examSession"])
app.include_router(exam_checking_router, prefix="/api/exams/checking", tags=["examChecking"])
app.include_router(student_dashboard_router, prefix="/api/student", tags=["studentDashboard"])
auto_submit_task = None

async def auto_submit_expired_sessions():
    from Models.Exam import Exam  # avoid circular import
    while True:
        try:
            redis = await get_redis()
            # List all keys matching exam_session:*
            keys = await redis.keys("exam_session:*")
            for key in keys:
                session = await redis.hgetall(key)
                if not session or session.get("status") != "active":
                    continue
                start_time = session.get("start_time")
                duration = int(session.get("duration", "0"))
                if not start_time or not duration:
                    continue
                start_dt = datetime.fromisoformat(start_time)
                end_dt = start_dt + timedelta(seconds=duration)
                if datetime.utcnow() >= end_dt:
                    # Auto-submit
                    db = next(get_db())
                    try:
                        answers = json.loads(session.get("answers", "{}"))
                        attempt = StudentExamAttempt(
                            exam_id=session["exam_id"],
                            student_id=session["student_id"],
                            started_at=start_dt,
                            completed_at=datetime.utcnow(),
                            is_teacher_checked=False,
                            is_ai_checked=False,
                        )
                        db.add(attempt)
                        db.commit()
                        db.refresh(attempt)
                        for qid, ans in answers.items():
                            db.add(StudentExamAnswer(
                                attempt_id=attempt.id,
                                question_id=qid,
                                answer=ans
                            ))
                        db.commit()
                        await redis.delete(key)
                    except Exception as e:
                        print(f"[AutoSubmit] Error: {e}")
                    finally:
                        db.close()
        except Exception as e:
            print(f"[AutoSubmit] Error: {e}")
        await asyncio.sleep(60)

@app.on_event("startup")
async def startup_event():
    await init_redis()
    await init_mongodb()
    global auto_submit_task
    auto_submit_task = asyncio.create_task(auto_submit_expired_sessions())

@app.on_event("shutdown")
async def shutdown_event():
    await close_redis()
    global auto_submit_task
    if auto_submit_task:
        auto_submit_task.cancel()


